using Alicres.SerialPort.Models;
using FluentAssertions;
using Xunit;

namespace Alicres.SerialPort.Tests.Models;

/// <summary>
/// BufferStatistics 类的单元测试
/// </summary>
public class BufferStatisticsTests
{
    [Fact]
    public void GetPerformanceSuggestions_WithHighQueueUsage_ShouldSuggestOptimization()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            QueueUsagePercentage = 85,
            TotalBytesReceived = 1000,
            TotalBytesDropped = 0,
            OverflowStrategy = BufferOverflowStrategy.DropOldest
        };

        // Act
        var suggestions = stats.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().NotBeEmpty();
        suggestions.Should().Contain("考虑增加队列最大长度或优化数据处理速度");
    }

    [Fact]
    public void GetPerformanceSuggestions_WithHighDataLossRate_ShouldSuggestCheckingLogic()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            QueueUsagePercentage = 50,
            TotalBytesReceived = 1000,
            TotalBytesDropped = 55,
            OverflowStrategy = BufferOverflowStrategy.DropOldest
        };

        // Act
        var suggestions = stats.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().NotBeEmpty();
        suggestions.Should().Contain("数据丢失率较高，建议检查数据处理逻辑");
    }

    [Fact]
    public void GetPerformanceSuggestions_WithDropNewestStrategy_ShouldWarnAboutDiscontinuity()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            QueueUsagePercentage = 50,
            TotalBytesReceived = 1000,
            TotalBytesDropped = 0,
            OverflowStrategy = BufferOverflowStrategy.DropNewest
        };

        // Act
        var suggestions = stats.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().NotBeEmpty();
        suggestions.Should().Contain("当前使用丢弃最新数据策略，可能导致数据不连续");
    }

    [Fact]
    public void GetPerformanceSuggestions_WithExceptionStrategyButDataLoss_ShouldSuggestCheckingExceptionHandling()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            QueueUsagePercentage = 50,
            DataLossRate = 0,
            OverflowStrategy = BufferOverflowStrategy.ThrowException,
            TotalBytesDropped = 100
        };

        // Act
        var suggestions = stats.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().NotBeEmpty();
        suggestions.Should().Contain("使用异常策略但仍有数据丢失，建议检查异常处理逻辑");
    }

    [Fact]
    public void GetPerformanceSuggestions_WithGoodConditions_ShouldReturnPositiveFeedback()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            QueueUsagePercentage = 50,
            DataLossRate = 0,
            OverflowStrategy = BufferOverflowStrategy.DropOldest,
            TotalBytesDropped = 0
        };

        // Act
        var suggestions = stats.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().NotBeEmpty();
        suggestions.Should().Contain("缓冲区运行状态良好，无需特别优化");
    }

    [Fact]
    public void GetHealthStatus_WithHighQueueUsage_ShouldReturnDangerStatus()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            QueueUsagePercentage = 95,
            DataLossRate = 0
        };

        // Act
        var status = stats.GetHealthStatus();

        // Assert
        status.Should().Be("危险 - 队列使用率过高");
    }

    [Fact]
    public void GetHealthStatus_WithModerateQueueUsage_ShouldReturnWarningStatus()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            QueueUsagePercentage = 85,
            DataLossRate = 0
        };

        // Act
        var status = stats.GetHealthStatus();

        // Assert
        status.Should().Be("警告 - 队列使用率较高");
    }

    [Fact]
    public void GetHealthStatus_WithHighDataLossRate_ShouldReturnWarningStatus()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            QueueUsagePercentage = 50,
            DataLossRate = 6.0
        };

        // Act
        var status = stats.GetHealthStatus();

        // Assert
        status.Should().Be("警告 - 数据丢失率较高");
    }

    [Fact]
    public void GetHealthStatus_WithLowDataLossRate_ShouldReturnAttentionStatus()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            QueueUsagePercentage = 50,
            DataLossRate = 2.0
        };

        // Act
        var status = stats.GetHealthStatus();

        // Assert
        status.Should().Be("注意 - 存在数据丢失");
    }

    [Fact]
    public void GetHealthStatus_WithGoodConditions_ShouldReturnGoodStatus()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            QueueUsagePercentage = 50,
            DataLossRate = 0
        };

        // Act
        var status = stats.GetHealthStatus();

        // Assert
        status.Should().Be("良好 - 运行正常");
    }

    [Fact]
    public void DataLossRate_WithNoReceivedData_ShouldReturnZero()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            TotalBytesReceived = 0,
            TotalBytesDropped = 100
        };

        // Act
        var rate = stats.DataLossRate;

        // Assert
        rate.Should().Be(0);
    }

    [Fact]
    public void DataLossRate_WithReceivedData_ShouldCalculateCorrectly()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            TotalBytesReceived = 1000,
            TotalBytesDropped = 50
        };

        // Act
        var rate = stats.DataLossRate;

        // Assert
        rate.Should().Be(5.0);
    }

    [Fact]
    public void GetDetailedReport_ShouldContainAllRelevantInformation()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            QueueLength = 100,
            MaxQueueLength = 200,
            QueueUsagePercentage = 50,
            TotalBytesReceived = 1000,
            TotalBytesDropped = 50,
            OverflowStrategy = BufferOverflowStrategy.DropOldest,
            LastCleanupTime = DateTime.Now.AddMinutes(-5)
        };

        // Act
        var report = stats.GetDetailedReport();

        // Assert
        report.Should().Contain("=== 缓冲区统计报告 ===");
        report.Should().Contain("队列状态: 100/200 (50%)");
        report.Should().Contain("总接收: 1,000 字节");
        report.Should().Contain("总丢弃: 50 字节");
        report.Should().Contain("丢失率: 5.00%");
        report.Should().Contain("溢出策略: DropOldest");
        report.Should().Contain("健康状态:");
    }

    [Fact]
    public void ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var stats = new BufferStatistics
        {
            QueueLength = 100,
            MaxQueueLength = 200,
            QueueUsagePercentage = 50,
            TotalBytesReceived = 1000,
            TotalBytesDropped = 50,
            OverflowStrategy = BufferOverflowStrategy.DropOldest
        };

        // Act
        var result = stats.ToString();

        // Assert
        result.Should().Contain("缓冲区统计");
        result.Should().Contain("队列 100/200 (50%)");
        result.Should().Contain("接收 1000 字节");
        result.Should().Contain("丢弃 50 字节 (5.00%)");
        result.Should().Contain("策略: DropOldest");
    }
}
